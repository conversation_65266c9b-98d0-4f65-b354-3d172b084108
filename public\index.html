<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Voice Chat Geyser</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        button {
            padding: 15px 30px;
            margin: 10px;
            font-size: 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .record-btn {
            background-color: #4CAF50;
            color: white;
        }
        .record-btn:hover {
            background-color: #45a049;
        }
        .stop-btn {
            background-color: #f44336;
            color: white;
        }
        .stop-btn:hover {
            background-color: #da190b;
        }
        .status {
            text-align: center;
            margin: 20px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .recording {
            background-color: #ffeb3b;
        }
        .connected {
            background-color: #4CAF50;
            color: white;
        }
        .disconnected {
            background-color: #f44336;
            color: white;
        }
        .users-list {
            margin: 20px 0;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Simple Voice Chat Geyser</h1>
        <div id="status" class="status disconnected">Disconnected</div>
        
        <div class="controls">
            <button id="recordBtn" class="record-btn">Start Recording</button>
            <button id="stopBtn" class="stop-btn" disabled>Stop Recording</button>
        </div>
        
        <div class="users-list">
            <h3>Connected Users: <span id="userCount">0</span></h3>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script src="script.js"></script>
</body>
</html>
