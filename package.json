{"name": "simple-voice-chat-geyser", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "socket.io": "^4.8.1", "ws": "^8.18.3"}, "devDependencies": {"nodemon": "^3.1.10"}}