const socket = io();
let mediaRecorder;
let audioChunks = [];
let isRecording = false;

const recordBtn = document.getElementById('recordBtn');
const stopBtn = document.getElementById('stopBtn');
const status = document.getElementById('status');
const userCount = document.getElementById('userCount');

// Socket connection events
socket.on('connect', () => {
    status.textContent = 'Connected';
    status.className = 'status connected';
});

socket.on('disconnect', () => {
    status.textContent = 'Disconnected';
    status.className = 'status disconnected';
});

// Handle incoming audio messages
socket.on('audio-message', (audioData) => {
    playAudio(audioData);
});

// Get user media and set up MediaRecorder
async function initAudio() {
    try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        mediaRecorder = new MediaRecorder(stream);
        
        mediaRecorder.ondataavailable = (event) => {
            audioChunks.push(event.data);
        };
        
        mediaRecorder.onstop = () => {
            const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
            audioChunks = [];
            
            // Convert blob to base64 and send
            const reader = new FileReader();
            reader.onload = () => {
                const audioData = reader.result.split(',')[1]; // Remove data:audio/wav;base64,
                socket.emit('audio-message', audioData);
            };
            reader.readAsDataURL(audioBlob);
        };
        
        recordBtn.disabled = false;
    } catch (error) {
        console.error('Error accessing microphone:', error);
        status.textContent = 'Microphone access denied';
        status.className = 'status disconnected';
    }
}

// Play received audio
function playAudio(base64Data) {
    const audio = new Audio(`data:audio/wav;base64,${base64Data}`);
    audio.play().catch(error => {
        console.error('Error playing audio:', error);
    });
}

// Record button event
recordBtn.addEventListener('click', () => {
    if (!isRecording && mediaRecorder) {
        mediaRecorder.start();
        isRecording = true;
        recordBtn.disabled = true;
        stopBtn.disabled = false;
        status.textContent = 'Recording...';
        status.className = 'status recording';
    }
});

// Stop button event
stopBtn.addEventListener('click', () => {
    if (isRecording && mediaRecorder) {
        mediaRecorder.stop();
        isRecording = false;
        recordBtn.disabled = false;
        stopBtn.disabled = true;
        status.textContent = 'Connected';
        status.className = 'status connected';
    }
});

// Initialize audio when page loads
window.addEventListener('load', initAudio);
