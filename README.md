# Simple Voice Chat Geyser

A simple real-time voice chat application built with Node.js, Socket.io, and WebRTC technologies.

## Features

- Real-time voice communication
- Simple and intuitive user interface
- Web-based (no installation required)
- Multi-user support
- Connection status indicators

## Prerequisites

- Node.js (v14 or higher)
- npm (Node Package Manager)
- Modern web browser with microphone support

## Installation

1. Clone or download the project
2. Navigate to the project directory:
   ```bash
   cd "Simple Voice Chat Geyser"
   ```
3. Install dependencies:
   ```bash
   npm install
   ```

## Usage

### Development Mode
Start the server in development mode with auto-reload:
```bash
npm run dev
```

### Production Mode
Start the server in production mode:
```bash
npm start
```

The application will be available at `http://localhost:3000`

## How to Use

1. Open your web browser and navigate to `http://localhost:3000`
2. Allow microphone access when prompted
3. Click "Start Recording" to begin voice transmission
4. Click "Stop Recording" to stop voice transmission
5. Other connected users will hear your voice messages in real-time

## Project Structure

```
Simple Voice Chat Geyser/
├── src/
│   └── index.js          # Server-side logic
├── public/
│   ├── index.html        # Client-side interface
│   └── script.js         # Client-side JavaScript
├── config/               # Configuration files
├── docs/                 # Documentation
├── package.json          # Project dependencies
├── .env                  # Environment variables
└── README.md             # This file
```

## Technologies Used

- **Backend**: Node.js, Express.js, Socket.io
- **Frontend**: HTML5, CSS3, JavaScript
- **Real-time Communication**: WebSockets
- **Audio Processing**: Web Audio API, MediaRecorder API

## Contributing

1. Fork the project
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a new Pull Request

## License

This project is licensed under the ISC License.

## Support

If you encounter any issues, please check:
1. Your browser supports the required APIs
2. Microphone permissions are granted
3. Network connectivity is stable

For technical support, please create an issue in the project repository.
